import React, { useRef } from "react";
import { useNavigate } from "react-router-dom";
import { sLogin } from "./loginStore";
import Loading from "./partials/Loading";
import { loginEmail } from "./services/loginUserByEmail";
// <EMAIL>
// SHIFT + ALT + O: Organize imports
// SHIFT + ALT + F: Format code
// CTRL + P: Search file
// CTRL + SHIFT + F: Search in files
export default function Login() {
  const isLoading = sLogin.use();
  const inputRef = useRef(null);
  const nav = useNavigate();
  const handleClick = () => {
    sLogin.set(true);
    loginEmail(inputRef.current.value, nav);
  };
  if (isLoading) {
    return <Loading />;
  }
  return (
    <div>
      <input ref={inputRef} type="email" placeholder="Email" />\
      <button onClick={handleClick}>Submit</button>
    </div>
  );
}
