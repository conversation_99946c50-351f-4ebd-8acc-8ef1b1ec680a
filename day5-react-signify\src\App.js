import { useEffect } from "react";
import User from "./components/User";
import { sUser } from "./store";

function App() {
  console.log("App123");

  sUser.watch((newValue) => {
    console.log("App", newValue);
  });

  useEffect(() => {
    sUser.stop();
  }, []);
  return (
    <div
      className="App"
      style={{
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <sUser.Wrap>
        {(user) => (
          <>
            <p>Name: {user.name}</p>
            <p>Age: {user.age}</p>
          </>
        )}
      </sUser.Wrap>
      <User />
      <sUser.DevTool name="User" />
    </div>
  );
}

export default App;
