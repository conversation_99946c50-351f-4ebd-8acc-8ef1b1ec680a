import React, { useState } from "react";

const Child = ({ name }) => {
  return (
    <>
      <p>{name}</p>
      <input />
    </>
  );
};

export default function Bai8() {
  const [isAdmin, setIsAdmin] = useState(true);
  return (
    <div>
      <button
        onClick={() => {
          setIsAdmin((prev) => !prev);
        }}
      >
        Change role
      </button>
      {isAdmin ? <Child key={1} name="admin" /> : <Child key={2} name="user" />}
    </div>
  );
}
