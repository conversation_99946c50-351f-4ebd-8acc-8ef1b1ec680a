import { getApiUser, KEY } from "./config";

export const getUser = async (id) => {
  const url = getApiUser(id);
  const res = await fetch(url, {
    method: "GET",
    headers: {
      accept: "application/json",
      key: KEY,
    },
  });

  if (!res.ok) {
    alert("Not found!");
  }
  return res.json();
};

export const addUser = async (user) => {
  const url = getApiUser();
  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      key: KEY,
    },
    body: JSON.stringify(user),
  });

  if (!res.ok) {
    alert("Add failed!");
    return;
  }
  return getUser();
};
