import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import Header from "../layouts/Header";
import Footer from "../layouts/Footer";
import Login from "../pages/Login/Login";
import ProtectRoute from "./ProtectRoute";
import ListUser from "../pages/ListUser/ListUser";
import EditPage from "../pages/Edit/EditPage";
import AddPage from "../pages/Add/AddPage";
import DeletePage from "../pages/Delete/DeletePage";

export default function MainRoutes() {
  return (
    <BrowserRouter>
      <Header />
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route
          path="/"
          element={
            <ProtectRoute>
              <ListUser />
            </ProtectRoute>
          }
        />
        <Route
          path="edit/:id"
          element={
            <ProtectRoute>
              <EditPage />
            </ProtectRoute>
          }
        />
        <Route
          path="add"
          element={
            <ProtectRoute>
              <AddPage />
            </ProtectRoute>
          }
        />
        <Route
          path="delete/:id"
          element={
            <ProtectRoute>
              <DeletePage />
            </ProtectRoute>
          }
        />
      </Routes>
      <Footer />
    </BrowserRouter>
  );
}
