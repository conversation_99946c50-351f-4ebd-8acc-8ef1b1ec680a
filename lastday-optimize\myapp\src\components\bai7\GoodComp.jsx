import React from "react";
import SlowComp from "../../SlowComp";

//childen khi nhieu hon 1 element se la array
//children khi chi co 1 element se la node
const Child = ({ children }) => {
  const admin = [1, 2, 4, 5, 7];
  const [count, setCount] = React.useState(0);
  const handleUp = () => {
    setCount((prev) => prev + 1);
  };
  console.log(children);

  return (
    <>
      {children.filter((_, index) => admin.includes(index))}
      <button onClick={handleUp}>up</button>
    </>
  );
};

export default function GoodComponent() {
  // consider using to create Navbar
  return (
    <div>
      <Child>
        <p>1</p>
        <p>2</p>
        <p>3</p>
      </Child>
      <SlowComp />
    </div>
  );
}
