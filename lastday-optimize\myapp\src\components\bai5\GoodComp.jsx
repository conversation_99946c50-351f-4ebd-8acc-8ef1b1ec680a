import React, { useState } from "react";
import SlowComp from "../../SlowComp";

const Child = ({ children }) => {
  const [count, setCount] = useState(0);

  const handleUp = () => {
    setCount((prev) => prev + 1);
  };
  return (
    <>
      {children(count)}
      <button onClick={handleUp}>Up count</button>
    </>
  );
};
// State needs to be moved to the child component
// but UI {count} is still in the parent component
export default function GoodComp() {
  return (
    <div>
      <p>GoodComp {0}</p>
      <Child>{(count) => <p>{count}</p>}</Child>
      <SlowComp />
    </div>
  );
}
