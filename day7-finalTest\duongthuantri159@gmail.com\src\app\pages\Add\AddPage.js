import React, { useState } from "react";
import http from "../../modules/Http";
import { useNavigate } from "react-router-dom";
import "./add.css";
export default function AddPage() {
  const [email, setEmail] = useState("");
  const [gender, setGender] = useState("");
  const [name, setName] = useState("");
  const [address, setAddress] = useState("");
  const [dob, setDob] = useState("");
  const [phone, setPhone] = useState("");
  const [avatar, setAvatar] = useState("");
  const nav = useNavigate();
  const handleSubmit = async () => {
    try {
      const res = await http.post("/user/", {
        email,
        gender,
        name,
        address,
        dob,
        phone,
        avatar,
      });
      console.log(res);
      if (res.ok) {
        nav("/");
      } else {
        alert("Failed to add user");
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <div className="add-page">
      <h2
        style={{
          margin: "8px",
        }}
      >
        Add New User
      </h2>
      <div className="input-container">
        <label htmlFor="email">Email:</label>
        <input
          type="text"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      <div className="input-container">
        <label htmlFor="gender">Gender</label>
        <input
          type="text"
          id="gender"
          value={gender}
          onChange={(e) => setGender(e.target.value)}
          required
        />
      </div>
      <div className="input-container">
        <label htmlFor="name">Name</label>
        <input
          type="text"
          id="name"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
        />
      </div>
      <div className="input-container">
        <label htmlFor="address">Address</label>
        <input
          type="text"
          id="address"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          required
        />
      </div>
      <div className="input-container">
        <label htmlFor="dob">Dob</label>
        <input
          type="text"
          id="dob"
          value={dob}
          onChange={(e) => setDob(e.target.value)}
          required
        />
      </div>
      <div className="input-container">
        <label htmlFor="phone">Phone</label>
        <input
          type="text"
          id="phone"
          value={phone}
          onChange={(e) => setPhone(e.target.value)}
          required
        />
      </div>
      <div className="input-container">
        <label htmlFor="avatar">Avatar</label>
        <input
          type="text"
          id="avatar"
          value={avatar}
          onChange={(e) => setAvatar(e.target.value)}
          required
        />
      </div>
      <button
        style={{
          padding: "10px",
          margin: "10px",
          fontSize: "20px",
          backgroundColor: "green",
          color: "white",
          border: "none",
          borderRadius: "5px",
        }}
        onClick={handleSubmit}
      >
        Submit
      </button>
    </div>
  );
}
