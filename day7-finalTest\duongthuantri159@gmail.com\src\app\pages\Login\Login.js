import React, { useState } from "react";
import http from "../../modules/Http";
import { useNavigate } from "react-router-dom";

export default function Login() {
  const [email, setEmail] = useState("");
  const nav = useNavigate();
  const handleSubmit = async () => {
    try {
      const res = await http.post("/login/", { email });
      console.log(res);
      if (res.ok) {
        localStorage.setItem("email", email);
        nav("/");
      } else {
        alert("Login failed");
      }
    } catch (err) {
      console.log(err);
    }
  };
  return (
    <div>
      <input
        type="email"
        required
        placeholder="Email"
        onChange={(e) => setEmail(e.target.value)}
      />
      <button onClick={handleSubmit}>Submit</button>
    </div>
  );
}
