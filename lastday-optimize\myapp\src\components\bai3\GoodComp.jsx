import React, { useState } from "react";

export default function GoodComp({ ui }) {
  // ui: Component as Prop - because ui is a prop, react will not re-render the ui component when the state of GoodComp changes

  const [count, setCount] = useState(0);

  const handleUp = () => {
    setCount(count + 1);
  };
  return (
    <div>
      <p>GoodComp {count}</p>
      <button onClick={handleUp}>Up </button>
      {ui}
    </div>
  );
}
