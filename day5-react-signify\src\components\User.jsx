import React from "react";
import { sUser } from "../store";

const ssAge = sUser.slice((n) => n.age);

export default function User() {
  console.log("User123");
  const handleAddAge = () => {
    sUser.set((n) => {
      n.value.age += 1;
    });
    if (sUser.value.age === 25) {
      sUser.resume();
    }
  };
  return (
    <div>
      <button onClick={handleAddAge}>Up age</button>
      <ssAge.Wrap>{(n) => n}</ssAge.Wrap>
    </div>
  );
}
