import { BE_ENDPOINT } from "../../../settings/localVar";

const KEY = "tri123";
const HEADERS = {
  "Content-Type": "application/json",
  accept: "application/json",
  key: KEY,
};
// url: protocal + domain + uri
export const get = async (uri, onSuccess, onFail) => {
  const response = await fetch(BE_ENDPOINT + uri, {
    headers: HEADERS,
  });
  if (!response.ok) {
    onFail();
    return;
  }
  const data = await response.json();
  onSuccess(data);
};
export const post = async (uri, reqData, onSuccess, onFail) => {
  const response = await fetch(BE_ENDPOINT + uri, {
    method: "POST",
    headers: HEADERS,
    body: JSON.stringify(reqData),
  });
  if (!response.ok) {
    onFail();
    return;
  }
  const data = await response.json();
  onSuccess(data);
};
