import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import http from "../../modules/Http";
import "./delete.css";
export default function DeletePage() {
  const { id } = useParams();
  const [email, setEmail] = useState("");
  const nav = useNavigate();
  console.log(id);
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await http.get("/user/?id=" + id);
        console.log(res);
        if (res.ok) {
          const data = await res.json();
          setEmail(data[0].email);
        } else {
          alert("Failed to fetch user");
          nav("/");
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchUser();
  }, [id]);
  const handleDelete = async () => {
    try {
      const res = await http.delete("/user/?id=" + id);
      console.log(res);
      if (res.ok) {
        nav("/");
      } else {
        alert("Failed to delete user");
      }
    } catch (err) {
      console.log(err);
    }
  };
  return (
    <div className="delete-page">
      <p className="email">{email}</p>
      <p className="confirm">Bạn chắc chắn muốn xoá User này?</p>
      <button
        style={{
          padding: "10px",
          margin: "10px",
          fontSize: "20px",
          backgroundColor: "red",
          color: "white",
          border: "none",
          borderRadius: "5px",
        }}
        onClick={handleDelete}
      >
        Xoá
      </button>
    </div>
  );
}
