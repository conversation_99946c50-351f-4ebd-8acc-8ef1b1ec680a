import { Browser<PERSON>out<PERSON>, <PERSON>, Route, Routes } from "react-router-dom";
import "./App.css";
import { About, Home } from "./pages";
import User from "./pages/User";
import PrivateRoute from "./pages/PrivateRoute";
import { lazy, Suspense } from "react";
const Details = lazy(() => import("./pages/Details"));
function App() {
  return (
    <>
      <div
        className="header"
        style={{
          display: "flex",
          width: "20%",
          margin: "auto",
          justifyContent: "space-between",
        }}
      >
        <Link to={"/"}>Home</Link>
        <Link to={"/about"}>About</Link>
        <Link to={"/user"}>User</Link>
      </div>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/user" element={<User />}>
          <Route
            path=":id"
            element={
              <PrivateRoute>
                <Suspense fallback={<div>Loading...</div>}>
                  <Details />
                </Suspense>
              </PrivateRoute>
            }
          />
        </Route>
        {/*or: <Route path="/users" element={<Details />} /> 
        URL example: https://localhost:3000/users?id=1
        import { useSearchParams } from "react-router-dom";

function Details() {
	const [searchParams] = useSearchParams();
  const id = searchParams.get("id");

	return <p>User ID: {id}</p>
}

         */}
      </Routes>
      <div className="footer">Footer</div>
      {/* Trong buổi hôm nay, mong mọi người sẽ nắm được cách vận hành như:
Tạo và triển khai React-Router-Dom ✨
Di chuyển giao diện với Link và useNavigate
Nested Routing để tạo các route con
Search Query Params để truyền biến đa dụng
URL Params để truyền thông tin đơn lẻ
Private Route để bảo mật quyền truy cập
Lazy loading Component để tối ưu hoá SEO và bảo mật mã nguồn
Lazy loading library để giới hạn lưu lượng package tải về, tăng điểm SEO */}
    </>
  );
}

export default App;
