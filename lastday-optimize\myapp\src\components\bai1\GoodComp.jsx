import React, { useState, memo, useCallback, useMemo } from "react";
import SlowComp from "../../SlowComp";

const Child = memo(({ handleUp, user }) => {
  return (
    <>
      <p>Child</p> <button onClick={handleUp}>Up </button>
      <SlowComp />
    </>
  );
}); // memo is used to prevent re-rendering of Child component (memo cache the props of param component, if it's not changed, the component will not re-render)
export default function GoodComp() {
  const [count, setCount] = useState(0);

  const handleUp = useCallback(() => {
    setCount((pre) => pre + 1);
  }, []);

  const user = useMemo(() => [], []); // if we dont wanna change the user, we can use useMemo to cache the user, user will not be changed when the component re-render
  return (
    <div>
      <p>GoodComp {count}</p>
      <Child handleUp={handleUp} user={user} />
    </div>
  );
}
