import React, { useEffect, useState } from "react";
import http from "../../modules/Http";
import { useNavigate } from "react-router-dom";
import "./listUser.css";
export default function ListUser() {
  const [users, setUsers] = useState([]);
  const nav = useNavigate();
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const res = await http.get("/user/");
        console.log(res);
        if (res.ok) {
          const data = await res.json();
          setUsers(data);
        } else {
          alert("Failed to fetch data");
        }
      } catch (err) {
        console.log(err);
      }
    };
    fetchUsers();
  }, []);
  const handleAdd = () => {
    nav("/add");
  };
  const handleEdit = async (id) => {
    nav("/edit/" + id);
  };
  const handleDelete = async (id) => {
    nav("/delete/" + id);
  };
  return (
    <div>
      <button
        style={{
          padding: "10px",
          margin: "10px",
          fontSize: "20px",
          backgroundColor: "green",
          color: "white",
          border: "none",
          borderRadius: "5px",
        }}
        onClick={handleAdd}
      >
        Add
      </button>
      <table
        style={{
          width: "100%",
          borderCollapse: "collapse",
          border: "1px solid black",
        }}
      >
        <thead
          style={{
            backgroundColor: "#80C4E9",
            color: "black",
            fontWeight: "bold",
            fontSize: "20px",
          }}
        >
          <tr
            style={{
              border: "1px solid black",
            }}
          >
            <th>Name</th>
            <th>Gender</th>
            <th>Email</th>
            <th>Dob</th>
            <th>Phone</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody
          style={{
            border: "1px solid black",
          }}
        >
          {users.map((user) => (
            <tr
              style={{
                border: "1px solid black",
                width: "100%",
              }}
              key={user.id}
            >
              <td>{user.name}</td>
              <td>{user.gender}</td>
              <td>{user.email}</td>
              <td>{user.dob}</td>
              <td>{user.phone}</td>
              <td>
                <button
                  style={{
                    padding: "5px",
                    margin: "5px",
                    fontSize: "15px",
                    backgroundColor: "blue",
                    color: "white",
                    border: "none",
                    borderRadius: "5px",
                  }}
                  onClick={() => {
                    handleEdit(user.id);
                  }}
                >
                  Edit
                </button>
                <button
                  style={{
                    padding: "5px",
                    margin: "5px",
                    fontSize: "15px",
                    backgroundColor: "red",
                    color: "white",
                    border: "none",
                    borderRadius: "5px",
                  }}
                  onClick={() => {
                    handleDelete(user.id);
                  }}
                >
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
