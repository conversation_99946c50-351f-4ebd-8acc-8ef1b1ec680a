import React, { useEffect, useRef, useState } from "react";
import { addUsers, deleteUsers, getUsers, updateUsers } from "./service";

export default function App() {
  const [ls, setLs] = useState([]);
  const refInput = useRef({});

  useEffect(() => {
    getUsers().then((data) => {
      setLs(data);
    });
  }, []);

  const handleChange = (event) => {
    const { name, value } = event.target;
    refInput.current[name] = value;
  };

  const handleAdd = () => {
    addUsers(refInput.current).then((data) => {
      setLs(data);
    });
  };

  const handleUpdate = () => {
    updateUsers(refInput.current).then((data) => {
      setLs(data);
    });
  };

  const handeDelete = (id) => () => {
    deleteUsers(id).then((data) => {
      setLs(data);
    });
  };

  return (
    <div>
      <label>id</label>
      <input onChange={handleChange} name="id" /> <br />
      <label>email</label>
      <input onChange={handleChange} name="email" /> <br />
      <label>gender</label>
      <input onChange={handleChange} name="gender" /> <br />
      <label>name</label>
      <input onChange={handleChange} name="name" /> <br />
      <label>address</label>
      <input onChange={handleChange} name="address" /> <br />
      <label>dob</label>
      <input onChange={handleChange} name="dob" /> <br />
      <label>phone</label>
      <input onChange={handleChange} name="phone" /> <br />
      <label>avatar</label>
      <input onChange={handleChange} name="avatar" /> <br />
      <button onClick={handleAdd}>Add</button>
      <button onClick={handleUpdate}>Update</button>
      <table border={1} style={{ borderCollapse: "collapse" }}>
        <thead>
          <tr>
            <th>-</th>
            <th>id</th>
            <th>email</th>
            <th>gender</th>
            <th>name</th>
            <th>address</th>
            <th>dob</th>
            <th>phone</th>
            <th>avatar</th>
          </tr>
        </thead>
        <tbody>
          {ls.map((v, i) => {
            return (
              <tr key={v.id}>
                <td>
                  <button onClick={handeDelete(v.id)}>x</button>
                </td>
                <td>{v.id}</td>
                <td>{v.email}</td>
                <td>{v.gender}</td>
                <td>{v.name}</td>
                <td>{v.address}</td>
                <td>{v.dob}</td>
                <td>{v.phone}</td>
                <td>{v.avatar}</td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
