import { useEffect, useRef, useState } from "react";
import "./App.css";
import { addUser, getUser } from "./service";

function App() {
  const [userList, setUserList] = useState([]);
  const refInput = useRef({});
  useEffect(() => {
    getUser().then((data) => {
      console.log(data);

      setUserList(data);
    });
  }, []);

  const handleChange = ({ target }) => {
    refInput.current[target.name] = target.value;
  };

  const handleAdd = () => {
    console.log(refInput.current);
    addUser(refInput.current).then((data) => {
      setUserList(data);
    });
  };
  return (
    <div
      style={{
        width: "100%",
        margin: "auto",
      }}
    >
      <h1>User Management</h1>
      <label>email</label>
      <input type="text" name="email" onChange={handleChange} />
      <br />
      <label>gender</label>
      <input type="text" name="gender" onChange={handleChange} />
      <br />
      <label>name</label>
      <input type="text" name="name" onChange={handleChange} />
      <br />
      <label>address</label>
      <input type="text" name="address" onChange={handleChange} />
      <br />
      <label>dob</label>
      <input type="text" name="dob" onChange={handleChange} />
      <br />
      <label>phone</label>
      <input type="text" name="phone" onChange={handleChange} />
      <br />
      <label>avatar</label>
      <input type="text" name="avatar" onChange={handleChange} />
      <br />
      <button onClick={handleAdd}>Add</button>
      <button>Edit</button>
      <table
        border={1}
        style={{
          borderCollapse: "collapse",
        }}
      >
        <thead>
          <tr>
            <th>id</th>
            <th>email</th>
            <th>gender</th>
            <th>name</th>
            <th>address</th>
            <th>dob</th>
            <th>phone</th>
            <th>avatar</th>
          </tr>
        </thead>
        <tbody>
          {userList.map((user) => (
            <tr key={user.id}>
              <td>{user.id}</td>
              <td>{user.email}</td>
              <td>{user.gender}</td>
              <td>{user.name}</td>
              <td>{user.address}</td>
              <td>{user.dob}</td>
              <td>{user.phone}</td>
              <td>
                <img src={user.avatar} alt={user.avatar} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
// alt + shift + nhap tren duoi
// ctrl + shift + arrow

export default App;
