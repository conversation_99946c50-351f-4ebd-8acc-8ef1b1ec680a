import React, { useEffect, useState } from "react";
import User from "./components/User";
const ls = [1, 2, 3];

export default function App() {
  const [count, setCount] = useState(0);

  useEffect(() => {
    console.log("mounted");
  }, []);

  useEffect(() => {
    console.log("updating");
  }, [count]);

  const up = () => {
    setCount(count + 1);
  };
  return (
    <div>
      App {count}
      <div>
        <button onClick={up}>UP</button>
      </div>
      {count < 5 && <User name="viet" age="18" />}
      {/* {ls.map((v) => {
        return <User key={v} name="viet1" age={v} />;
      })} */}
    </div>
  );
}
