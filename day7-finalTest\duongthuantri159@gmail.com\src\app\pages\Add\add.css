.add-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 8px auto;
  background-color: #80c4e9;
  padding: 8px 16px;
  width: fit-content;
  border-radius: 16px;
  box-shadow: 0 0 10px 0 #000;
}
.input-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
  label {
    font-size: 16px;
    font-weight: 600;
  }
  input {
    padding: 5px;
    font-size: 16px;
    border-radius: 5px;
    border: 1px solid #ccc;
    width: 300px;
  }
}
