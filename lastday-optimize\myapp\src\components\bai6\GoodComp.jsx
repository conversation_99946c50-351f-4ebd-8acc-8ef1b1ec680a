import React, { useState } from "react";
import SlowComp from "../../SlowComp";

const Child = ({ children }) => {
  const [count, setCount] = useState(0);

  const handleUp = () => {
    setCount((prev) => prev + 1);
  };
  return (
    <>
      {children(count)}
      <button onClick={handleUp}>Up count</button>
    </>
  );
};

export default function GoodComp() {
  return (
    <div>
      <p>GoodComp {0}</p>
      <Child>
        {(count) => {
          if (count > 5) return <p>Count is too high</p>;
          else {
            return <p>{count}</p>;
          }
        }}
      </Child>
      <SlowComp />
    </div>
  );
}
