import { API_KEY, BE_ENDPOINT } from "../../settings/localVar";

class Http {
  baseUrl;
  API_KEY;
  constructor() {
    this.baseUrl = BE_ENDPOINT;
    this.API_KEY = API_KEY;
  }
  getURL(url) {
    return `${this.baseUrl}${url}`;
  }
  async get(endpoint) {
    const response = await fetch(this.getURL(endpoint), {
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
        key: this.API_KEY,
      },
    });
    console.log(response);
    return response;
  }
  async post(endpoint, data) {
    const response = await fetch(this.getURL(endpoint), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
        key: this.API_KEY,
      },
      body: JSON.stringify(data),
    });
    return response;
  }

  async put(endpoint, data) {
    const response = await fetch(this.getURL(endpoint), {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
        key: this.API_KEY,
      },
      body: JSON.stringify(data),
    });
    return response;
  }
  async delete(endpoint) {
    const response = await fetch(this.getURL(endpoint), {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        accept: "application/json",
        key: this.API_KEY,
      },
    });
    return response;
  }
}

const http = new Http();
export default http;
